# KULLM Pro Configuration File
# Production-ready configuration for Korean reasoning language model training with think tokens

# Model Configuration
model:
  name: "Qwen/Qwen2.5-7B-Instruct"  # Base model to fine-tune
  max_length: 8192                   # Maximum sequence length
  torch_dtype: "float16"             # Model precision (float16, bfloat16, float32)
  device_map: "auto"                 # Device mapping strategy
  trust_remote_code: false           # Whether to trust remote code

# Training Configuration
training:
  # Basic training parameters
  num_train_epochs: 3                # Number of training epochs
  per_device_train_batch_size: 2     # Training batch size per device
  per_device_eval_batch_size: 2      # Evaluation batch size per device
  gradient_accumulation_steps: 8     # Gradient accumulation steps

  # Optimization parameters
  learning_rate: 0.0002              # Learning rate
  weight_decay: 0.01                 # Weight decay
  warmup_ratio: 0.1                  # Warmup ratio
  lr_scheduler_type: "cosine"        # Learning rate scheduler

  # Checkpointing and evaluation
  save_steps: 500                    # Save checkpoint every N steps
  eval_steps: 500                    # Evaluate every N steps
  logging_steps: 10                  # Log every N steps
  save_total_limit: 3                # Maximum number of checkpoints to keep
  load_best_model_at_end: true       # Load best model at end of training
  metric_for_best_model: "eval_loss" # Metric to use for best model selection
  greater_is_better: false           # Whether higher metric is better

  # Training strategies
  eval_strategy: "steps"             # Evaluation strategy (steps, epoch, no)
  save_strategy: "steps"             # Save strategy (steps, epoch, no)

  # Performance optimizations
  fp16: true                         # Use mixed precision training
  gradient_checkpointing: true       # Use gradient checkpointing to save memory
  dataloader_pin_memory: false       # Pin memory for data loading
  remove_unused_columns: false       # Remove unused columns from dataset

# LoRA Configuration
lora:
  enabled: true                    # Enable/disable LoRA (set to false for full fine-tuning)
  r: 16                            # LoRA rank
  alpha: 32                        # LoRA alpha parameter
  dropout: 0.1                     # LoRA dropout
  bias: "none"                     # Bias configuration
  task_type: "CAUSAL_LM"          # Task type
  target_modules:                  # Target modules for LoRA
    - "q_proj"
    - "k_proj"
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

# Experiment Tracking (Weights & Biases)
wandb:
  project: "kullm-pro-reasoning"     # WandB project name
  enabled: true                      # Enable/disable WandB logging
  entity: "junkim100"                    # WandB entity (optional)
  tags: ["reasoning", "think-tokens", "kullm-pro"]  # Additional tags for runs

# Dataset Processing Configuration
dataset:
  # Think token configuration for reasoning models
  think_token_start: "<think>"       # Start token for reasoning
  think_token_end: "</think>"        # End token for reasoning

  # Data validation and processing
  min_solution_length: 10            # Minimum solution length in characters
  max_solution_length: 10000         # Maximum solution length in characters
  shuffle_data: true                 # Shuffle dataset before processing

# Code Switching Configuration
code_switching:
  # OpenAI API settings
  model: "o4-mini-2025-04-16"        # OpenAI o4-mini model for high reasoning effort
  max_completion_tokens: 2000        # Maximum completion tokens per request
  # Note: o4 models don't support temperature parameter

  # Batch processing settings
  batch_size: 100                    # Number of requests per batch
  check_interval: 60                 # Check interval for batch completion (seconds)

  # System prompt for code switching
  system_prompt: |
    You are a helpful assistant that translates English text to Korean.
    Translate the given English text to natural, fluent Korean while preserving the meaning and context.
    For mathematical expressions, formulas, and code, keep them as they are.
    Only translate the natural language portions.

# Logging Configuration
logging:
  level: "INFO"                      # Logging level
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: null                         # Optional log file path

# Default Paths (can be overridden by CLI arguments)
paths:
  data_dir: "./data"                 # Default data directory
  output_dir: "./outputs"            # Default output directory
  src_dir: "./src"                   # Source code directory
