import os
import time
import torch
import warnings
import re
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
from peft import PeftModel

# Suppress warnings for cleaner inference output
warnings.filterwarnings("ignore")
os.environ["TOKENIZERS_PARALLELISM"] = "false"


def load_model_and_tokenizer(model_path, lora_path=None):
    """
    Load model and tokenizer with optional LoRA adapter support.

    Args:
        model_path: Path to base model or fine-tuned model
        lora_path: Optional path to LoRA adapter

    Returns:
        tuple: (model, tokenizer)
    """
    print(f"Loading tokenizer from: {model_path}")
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)

    # Set padding token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
        tokenizer.pad_token_id = tokenizer.eos_token_id

    print(f"Loading model from: {model_path}")
    model = AutoModelForCausalLM.from_pretrained(
        model_path, torch_dtype=torch.float16, device_map="auto", trust_remote_code=True
    )

    # Load LoRA adapter if provided
    if lora_path:
        print(f"Loading LoRA adapter from: {lora_path}")
        model = PeftModel.from_pretrained(model, lora_path)
        print("✅ LoRA adapter loaded successfully")

    print("✅ Model loaded successfully")
    return model, tokenizer


def get_pipeline(model, tokenizer):
    """Create text generation pipeline."""
    generator = pipeline(
        "text-generation",
        model=model,
        tokenizer=tokenizer,
        pad_token_id=tokenizer.pad_token_id,
        eos_token_id=[tokenizer.eos_token_id, 128001],
    )
    return generator


def format_response_with_think_tokens(text):
    """
    Format response text to display think tokens in different colors.

    Args:
        text: Response text that may contain think tokens

    Returns:
        str: Formatted text with colored think tokens
    """
    # ANSI color codes
    THINK_COLOR = "\033[96m"  # Cyan for think tokens
    CONTENT_COLOR = "\033[94m"  # Blue for think content
    RESET_COLOR = "\033[0m"  # Reset to default
    ASSISTANT_COLOR = "\033[95m"  # Magenta for assistant text

    # Check if think tokens exist
    if "<think>" in text and "</think>" in text:
        # Replace think tokens with colored versions
        text = re.sub(
            r"<think>(.*?)</think>",
            f"{THINK_COLOR}<think>{CONTENT_COLOR}\\1{THINK_COLOR}</think>{RESET_COLOR}",
            text,
            flags=re.DOTALL,
        )
        return f"{ASSISTANT_COLOR}Assistant: {RESET_COLOR}{text}"
    else:
        # No think tokens, use regular assistant color
        return f"{ASSISTANT_COLOR}Assistant: {text}{RESET_COLOR}"


def load_system_prompt():
    """
    Load system prompt from code_switch.txt if it exists.

    Returns:
        str or None: System prompt content or None if not found
    """
    try:
        if os.path.exists("code_switch.txt"):
            with open("code_switch.txt", "r", encoding="utf-8") as f:
                content = f.read().strip()
                if content:
                    print("✅ Loaded system prompt from code_switch.txt")
                    return content
    except Exception as e:
        print(f"⚠️ Could not load system prompt: {e}")

    return None


SYS_PROMPT = load_system_prompt()


def main(
    model_path: str,
    lora_path: str = None,
    sys_prompt: str = SYS_PROMPT,
    max_new_tokens: int = 512,
    **kwargs,
):
    """
    Interactive chat interface with support for LoRA adapters and think tokens.

    Args:
        model_path: Path to base model or fine-tuned model
        lora_path: Optional path to LoRA adapter
        sys_prompt: System prompt (auto-loaded from code_switch.txt if available)
        max_new_tokens: Maximum tokens to generate
        **kwargs: Additional generation parameters
    """
    print("🚀 KULLM-Pro Chat Interface")
    print("=" * 50)
    print(f"Model path: {model_path}")
    if lora_path:
        print(f"LoRA path: {lora_path}")
    print()

    # Load model and tokenizer
    model, tokenizer = load_model_and_tokenizer(model_path, lora_path)

    # Display tokenizer info
    print(f"BOS token: {tokenizer.bos_token} (ID: {tokenizer.bos_token_id})")
    print(f"EOS token: {tokenizer.eos_token} (ID: {tokenizer.eos_token_id})")

    # Check for think tokens
    think_tokens_available = False
    if hasattr(tokenizer, "additional_special_tokens"):
        special_tokens = tokenizer.additional_special_tokens or []
        if "<think>" in special_tokens and "</think>" in special_tokens:
            think_tokens_available = True
            print("✅ Think tokens detected: <think>, </think>")

    if not think_tokens_available:
        print("ℹ️ No think tokens detected")

    print()

    # Create pipeline
    pipe = get_pipeline(model, tokenizer)
    # Initialize conversation
    messages = []
    if sys_prompt is not None:
        messages.append({"role": "system", "content": sys_prompt})
        print(f"✅ System prompt loaded ({len(sys_prompt)} characters)")

    print("\n💬 Chat started! Commands: 'clear' to reset, 'exit' to quit")
    print("=" * 50)

    while True:
        try:
            # Get user input
            input_ = input("\033[94m👤 Enter instruction: \033[0m")

            # Handle special commands
            if input_.strip().lower() == "clear":
                messages = []
                if sys_prompt:
                    messages.append({"role": "system", "content": sys_prompt})
                os.system("clear")
                print("🚀 KULLM-Pro Chat Interface")
                print("=" * 50)
                print("🔄 Conversation cleared!")
                print("=" * 50)
                continue
            elif input_.strip().lower() == "exit":
                print("\n👋 Goodbye!")
                break
            elif input_.strip() == "":
                continue

            # Add user message
            messages.append({"role": "user", "content": input_})

            # Clear screen and show conversation history
            os.system("clear")
            print("🚀 KULLM-Pro Chat Interface")
            print("=" * 50)

            # Display conversation history (excluding system prompt)
            for m in messages:
                if m["role"] == "system":
                    continue
                elif m["role"] == "user":
                    print(f"\033[93m👤 User: {m['content']}\033[0m")
                elif m["role"] == "assistant":
                    print(format_response_with_think_tokens(m["content"]))

            # Show current user input
            print(f"\033[93m👤 User: {input_}\033[0m")

            # Generate response
            print("\033[90m🤔 Thinking...\033[0m")
            start = time.time()

            # Apply chat template
            text = tokenizer.apply_chat_template(
                messages, add_generation_prompt=True, tokenize=False
            )

            # Generate response
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                result = pipe(
                    text,
                    return_full_text=False,
                    clean_up_tokenization_spaces=True,
                    max_new_tokens=max_new_tokens,
                    do_sample=kwargs.get("do_sample", False),
                    **kwargs,
                )[0]["generated_text"]

            # Add assistant response to conversation
            messages.append({"role": "assistant", "content": result})

            # Display formatted response
            print(format_response_with_think_tokens(result))
            print(f"\033[90m⏱️ Response time: {time.time() - start:.2f}s\033[0m")
            print("=" * 50)

        except KeyboardInterrupt:
            print("\n\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type 'exit' to quit.")
            continue


if __name__ == "__main__":
    import fire

    fire.Fire(main)
